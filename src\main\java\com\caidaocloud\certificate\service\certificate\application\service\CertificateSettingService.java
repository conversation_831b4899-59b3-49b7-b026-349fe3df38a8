package com.caidaocloud.certificate.service.certificate.application.service;

import com.caidaocloud.certificate.service.certificate.domain.base.util.LangUtil;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateSettingDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateSettingDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.SettingDto;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import com.googlecode.totallylazy.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/20 16:27
 **/
@Service
@Slf4j
public class CertificateSettingService {

    @Resource
    private CertificateSettingDo certificateSettingDo;
    @Resource
    private MetadataOperatorService metadataOperatorService;
    //员工任职信息
//    private static final Set<String> OCCUPY_EMP_PROPERTY = Sets.set("name", "hireDate", "organizeTxt", "postTxt","workno","jobGrade$startGradeName","jobTxt");
    //员工必填信息
    private static final Set<String> OCCUPY_MUST_PROPERTY = Sets.set("name", "organizeTxt", "postTxt","workno");
    //员工个人信息
//    private static final Set<String> PRIVATE_EMP_PROPERTY = Sets.set("sex", "email", "permanentAddress");
    public Object selectList(CertificateSettingDo dto) {
        if(!dto.getTypeCode().equals("msg")){
            List<CertificateSettingDo> settingDos = certificateSettingDo.selectList(dto);;
            List<SettingDto> dtos = coverSetDto(settingDos).stream().sorted(Comparator.comparing(SettingDto::getSort)).collect(Collectors.toList());
            return dtos;
        }
       return certificateSettingDo.selectList(dto);
    }

    public String add(CertificateSettingDo dto) {
        return certificateSettingDo.save(dto);
    }

    public void del(CertificateSettingDo dto) {
         certificateSettingDo.delete(dto);
    }

    public void updata(CertificateSettingDo dto) {
        certificateSettingDo.update(dto);
    }
    public void saveBatch(CeritificateSettingDto dto) {
        certificateSettingDo.saveBatch(dto);
    }


    public List<Map<String, Object>> get(CertificateSettingDo dto) {
        List<CertificateSettingDo> list = certificateSettingDo.selectList(dto);
        Map<String, List<CertificateSettingDo>> collect = null;
        if (CollectionUtils.isNotEmpty(list)){
            collect =list.stream().collect(Collectors.groupingBy(CertificateSettingDo::getMsgBid));
            List<Map<String, Object>> result=new ArrayList<>();
            for (Map.Entry<String, List<CertificateSettingDo>> entry : collect.entrySet()) {
                Map<String, Object> map=new HashMap<>();
                List<SettingDto> dtos=coverSetDto(entry.getValue());
                map.put("model",entry.getKey());
                map.put("properties",dtos);
                result.add(map);
            }
            return result;
        }

        return new ArrayList<>();
    }

    private List<SettingDto> coverSetDto(List<CertificateSettingDo> value) {
        List<SettingDto> dtos= new ArrayList<>();
        value.forEach(t->{
            SettingDto settingDto = new SettingDto();
            settingDto.setEntity(t.getMsgBid());
            settingDto.setBid(t.getBid());
            settingDto.setProperty(t.getMsgName());
            settingDto.setName(t.getCertificateName());
            settingDto.setRemake(t.getRemake());
            settingDto.setSort(t.getCertificateBid());
            dtos.add(settingDto);
        });
        return dtos;
    }

    public Object set() {
        List<Map<String,Object>> result=new ArrayList<>();
        Locale locale = LangUtil.getLocale();


        MetadataVo occupyMetadata = getMetadata("entity.hr.EmpWorkInfo");
        List<MetadataPropertyVo> occupyList = getMetadataProperty(occupyMetadata);
        InitResultMap(result, locale, occupyList, "员工任职信息", "entity.hr.EmpWorkInfo", occupyMetadata.getCustomProperties());

        MetadataVo privateMetadata = getMetadata("entity.hr.EmpPrivateInfo");
        List<MetadataPropertyVo> privateList = getMetadataProperty(privateMetadata);
        InitResultMap(result, locale, privateList, "员工个人信息", "entity.hr.EmpPrivateInfo", privateMetadata.getCustomProperties());
        return result;
    }

    private void InitResultMap(List<Map<String, Object>> result, Locale locale, List<MetadataPropertyVo> privateList, String name, String IDENTIFIER, List<MetadataPropertyVo> customProperties) {
        Map<String,Object> map=new HashMap<>();
        List<SettingDto> dtos = BeanUtil.convertList(privateList, SettingDto.class);
        //这里控制显示那些参数 privateEmpProperty 显示参数的集合
        dtos = Sequences.sequence(dtos)
                .map(mpv -> {
                    if ("entity.hr.EmpWorkInfo".equals(IDENTIFIER) && OCCUPY_MUST_PROPERTY.contains(mpv.getProperty())){
                        mpv.setRemake("true");
                    }
                    mpv.setEntity(name);
                    if (ObjectUtil.isNotEmpty(mpv.getI18nName())){
                        mpv.setName(LangUtil.getCurrentLangVal(mpv.getI18nName(), locale));
                    }
                    mpv.setProperty(IDENTIFIER+"@"+mpv.getProperty());
                    return mpv;
                }).toList();
        dtos.addAll(Sequences.sequence(customProperties).map(p -> {
            SettingDto mpv = ObjectConverter.convert(p, SettingDto.class);
            mpv.setEntity(name);
            if (ObjectUtil.isNotEmpty(mpv.getI18nName())) {
                mpv.setName(LangUtil.getCurrentLangVal(mpv.getI18nName(), locale));
            }
            mpv.setProperty(IDENTIFIER + "@ext@" + mpv.getProperty());
            return mpv;
        }));
        map.put("name",name);
        map.put("identifier",IDENTIFIER);
        map.put("properties",dtos);
        result.add(map);
    }

    public MetadataVo getMetadata(String identifier){
        MetadataVo metadata = metadataOperatorService.load(identifier);
        return metadata;
    }
    public List<MetadataPropertyVo> getMetadataProperty(MetadataVo metadata){
        List<MetadataPropertyVo> allProperties = Optional.ofNullable(metadata).map(MetadataVo::getStandardProperties).orElse(Lists.newArrayList());
        allProperties.addAll(Optional.ofNullable(metadata).map(MetadataVo::getInheritedStandardProperties).orElse(Lists.newArrayList()));
        // allProperties.addAll(Optional.ofNullable(metadata).map(MetadataVo::getCustomProperties).orElse(Lists.newArrayList()));
        return allProperties;
    }

    public Object dragSort(List<String>  ids) {
        int sort=0;
        for (String id : ids) {
            CertificateSettingDo settingDo = certificateSettingDo.selectById(id);
            settingDo.setCertificateBid(String.valueOf(sort++));
            certificateSettingDo.update(settingDo);
        }
        return true;
    }
}
