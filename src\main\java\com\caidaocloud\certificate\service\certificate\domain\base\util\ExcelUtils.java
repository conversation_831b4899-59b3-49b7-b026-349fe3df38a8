package com.caidaocloud.certificate.service.certificate.domain.base.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelBaseEntity;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.handler.impl.ExcelDataHandlerDefaultImpl;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.QualifiedPerFunVO;
import com.caidaocloud.excption.ServerException;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExcelUtils {
    public static void downloadDataMapExcel(List<ExcelExportEntity> colList, List<Map<String, Object>> list,
        String xlsFileName, HttpServletResponse response) {
        downloadDataListMapExcel(colList, list, xlsFileName, response);
    }

    public static void downloadDataListMapExcel(List<ExcelExportEntity> colList, List<?> list,
        String xlsFileName, HttpServletResponse response) {
        try {
            final Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), colList, list);
            downLoadExcel(xlsFileName, response, workbook);
        } catch (IOException e) {
            throw new ServerException("download excel err.");
        }
    }
    // 资格证书导出专用
    public static void downloadDataListCustom(List<ExcelExportEntity> colList, List<Map<String, Object>> maps, List<Map<String, Object>> mapList,
                                              String xlsFileName, HttpServletResponse response) {
        try {
            ExportParams params = new ExportParams();
            List<String> certificateTitles = Sequences.sequence(colList)
                    .filter(col -> ((String) col.getKey()).startsWith("certificateName"))
                    .map(ExcelBaseEntity::getName).toList();
            params.setDataHandler(new ExcelDataHandlerDefaultImpl() {
                @Override
                public String[] getNeedHandlerFields() {
                    return certificateTitles.toArray(new String[0]);
                }

                @Override
                public Object exportHandler(Object obj, String name, Object value) {
                    if (value == null) {
                        return "-";
                    }
                    return value;
                }
            });
            final Workbook workbook = ExcelExportUtil.exportExcel(params, colList, mapList);
//            设置背景颜色
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            if (CollectionUtils.isNotEmpty(maps)) {
                for (Row cells : workbook.getSheetAt(0)) {
                    if (cells.getRowNum() >= 1 && cells.getRowNum() <= maps.size()) {
                        Map<String, Object> map = maps.get(cells.getRowNum() - 1);
                        for (Cell cell : cells) {
                            if (cell.getCellType() == CellType.STRING && map.containsKey(cell.getStringCellValue())) {
                                QualifiedPerFunVO vo = (QualifiedPerFunVO) map.get(cell.getStringCellValue());
                                if (vo.getIsUse()) {
                                    cell.setCellValue("✓");
                                }
                                if (vo.getIsColor()) {
                                    cell.setCellStyle(cellStyle);
                                }
                                if (cell.getStringCellValue().contains( "证书")) {
                                    cell.setCellValue("-");
                                }
                            }
                        }
                    }
                }
            }
            downLoadExcel(xlsFileName, response, workbook);
        } catch (IOException e) {
            throw new ServerException("download excel err.");
        }
    }

    /**
     * excel下载
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     */
    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static void writeDataFile(File file, List<ExcelExportEntity> colList, List<Map<String, Object>> list, String xlsFileName) throws IOException {
        final Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), colList, list);
        workbook.write(new FileOutputStream(file));
    }

    public static List<ExcelExportEntity> buildExportEntity(Class clazz) {
        List<ExcelExportEntity> colList = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            Excel annotation = f.getAnnotation(Excel.class);
            if (null == annotation) {
                continue;
            }
            String name = annotation.name();
            String property = f.getName();
            String order = annotation.orderNum();
            double width = annotation.width();
            if (width == 0) {
                width = 13;
            }
            ExcelExportEntity entity = new ExcelExportEntity(name, property);
            entity.setWidth(width);
            if (null != order) {
                entity.setOrderNum(Integer.valueOf(order));
            }
            colList.add(entity);
        }
        return colList;
    }
}
