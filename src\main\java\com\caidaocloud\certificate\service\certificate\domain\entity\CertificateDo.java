package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Maps;
import groovy.util.logging.Slf4j;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书
 **/
@Data
@Slf4j
@Service
public class CertificateDo extends DataEntity {
    public static final String IDENTIFIER = "entity.certificate.certificate";

    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 序列编码
     */
    private Integer sortNum;
    /**
     * 状态
     */
    private EnumSimple status;
    /**
     * 证书类型
     */
    private String typeBid;
    /**
     * 证书名称多语言
     */
    private String i18nName;
    /**
     * 员工id
     */
    private String empId;
    /**
     * 项目id
     */
    private String proBid;


    /**
     * 保存
     * @param data
     */
    public String save(CertificateDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        DataEntity.initFieldValue(IDENTIFIER, BusinessEventTypeEnum.CREATE, data, null);
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setBid(SnowUtil.nextId());
        String dataId = DataInsert.identifier(data.getIdentifier()).insert(data);
        return dataId;
    }

    /**
     * 修改
     * @param data
     */
    public void update(CertificateDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        CertificateDo dbData = selectById(data.getBid());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        DataEntity.initFieldValue(IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        SpringUtil.getBean(ICertificateRepository.class).updateById(data);
    }

    /**
     * 查看详情
     * @param bid
     * @return
     */
    public CertificateDo selectById(String bid) {
        return SpringUtil.getBean(ICertificateRepository.class).selectById(bid, IDENTIFIER);
    }
    /**
     * 删除
     *
     * @param data
     */
    public void delete(CertificateDo data) {
        data.setIdentifier(IDENTIFIER);
        SpringUtil.getBean(ICertificateRepository.class).delete(data);
    }

    /**
     * 列表查询
     * @param dto
     * @return
     */
    public List<CertificateDo> selectList(CertificateQueryDto dto) {
        List<CertificateDo> list = SpringUtil.getBean(ICertificateRepository.class).selectList(dto, IDENTIFIER);
        // list = list.stream().sorted(Comparator.comparing(CertificateDo::getUpdateTime).reversed()).collect(Collectors.toList());
        return list;
    }

    public static List<CertificateDo> sortList() {
        CertificateQueryDto dto = new CertificateQueryDto();
        dto.setPageNo(1);
        dto.setPageSize(-1);
        return SpringUtil.getBean(ICertificateRepository.class).sortPageList(dto, IDENTIFIER).getItems();
    }

    public List<CertificateDo> all(CertificateQueryDto dto) {
        List<CertificateDo> list = SpringUtil.getBean(ICertificateRepository.class).selectList(dto, IDENTIFIER);
        // list = list.stream().sorted(Comparator.comparing(CertificateDo::getUpdateTime).reversed()).collect(Collectors.toList());
        return list;
    }

    public void updateStatus(CertificateDo data, BusinessEventTypeEnum enable) {
        CertificateDo dbData = selectById(data.getBid());
        check(dbData);
        DataEntity.initFieldValue(IDENTIFIER, enable, data, dbData);
        SpringUtil.getBean(ICertificateRepository.class).updateById(data);
    }

    private void check(CertificateDo data) {
        PreCheck.preCheckArgument(null == data || null == data.getBid(), "证书类型不存在");
    }

    public PageResult<CertificateDo> pageList(CertificateQueryDto queryDto) {
        return SpringUtil.getBean(ICertificateRepository.class).sortPageList(queryDto, IDENTIFIER);
    }

    public String getI18nName() {
        if (i18nName == null) {
            Map<String, String> map = Maps.map("default", getName());
            return FastjsonUtil.toJson(map);
        }
        return i18nName;
    }
}
