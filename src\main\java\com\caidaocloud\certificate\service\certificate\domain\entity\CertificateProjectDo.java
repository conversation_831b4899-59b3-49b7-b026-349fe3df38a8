package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatus;
import com.caidaocloud.certificate.service.certificate.domain.enums.ProjectStatus;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateProjectRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateUsageRecordRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/4/15
 */
@Data
public class CertificateProjectDo extends DataEntity {
	// 组织id
	private String organize;
	// 项目名称
	private String projectName;
	// 项目负责人
	private String empId;
	// 项目地点
	private String location;
	// 项目开始日期
	private Long startDate;
	// 项目结束日期
	private Long endDate;
	// 项目状态
	private ProjectStatus projectStatus;
	// 证书开始日期
	private Long certificateStartDate;
	// 证书结束日期
	private Long certificateEndDate;
	// 证书状态
	private CertificateStatus certificateStatus;

	public final static String  IDENTIFIER = "entity.certificate.CertificateProject";

	public CertificateProjectDo() {
		setIdentifier(IDENTIFIER);
	}

	public static Optional<CertificateProjectDo> findById(String bid) {
		return Optional.ofNullable(SpringUtil.getBean(ICertificateProjectRepository.class).selectById(bid, IDENTIFIER));
	}

	public static PageResult<CertificateProjectDo> page(CertificateProjectQueryDto dto) {
		PageResult<CertificateProjectDo> page = SpringUtil.getBean(ICertificateProjectRepository.class)
				.selectPage(dto);
		return page;
	}

	public static List<CertificateProjectDo> listById(List<String> proIds) {
		return SpringUtil.getBean(ICertificateProjectRepository.class)
				.listById(proIds);
	}

	public static List<CertificateUsageRecordDo> listRecordByRange(String projectId, Long startTime, Long endTime) {
		return SpringUtil.getBean(ICertificateUsageRecordRepository.class).listByRange(projectId, startTime, endTime);
	}

	public void save() {
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		if (getCreateTime() == 0L) {
			setCreateTime(getUpdateTime());
			setCreateBy(getUpdateBy());
		}
		updateStatus();
		checkOrganize();
		SpringUtil.getBean(ICertificateProjectRepository.class).save(this);
	}

	private void checkOrganize() {
		List<CertificateProjectDo> exists = SpringUtil.getBean(ICertificateProjectRepository.class)
				.selectByOrg(organize, getBid());
		if (!exists.isEmpty()) {
			throw new ServerException("该组织下已存在项目");
		}
	}

	private void updateStatus() {
		setProjectStatus(ProjectStatus.determineProjectStatus(startDate, endDate));
		setCertificateStatus(CertificateStatus.determineProjectStatus(certificateStartDate, certificateEndDate));
	}

	public void delete() {
		SpringUtil.getBean(ICertificateProjectRepository.class).delete(this);
	}
}
