package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateSettingDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.SettingDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import groovy.util.logging.Slf4j;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书
 **/
@Data
@Slf4j
@Service
public class CertificateSettingDo extends DataEntity {
    private static final String IDENTIFIER = "entity.certificate.certificateSet";

    /**
     * 类型code(废弃)
     */
    private String typeCode;
    /**
     * 证书名称
     */
    @JsonProperty(value = "CertificateName")
    @JSONField(name = "CertificateName")
    private String CertificateName;
    /**
     * 证书bid
     */
    @JsonProperty(value = "CertificateBid")
    @JSONField(name = "CertificateBid")
    private String CertificateBid;
    /**
     * 消息通知名称
     */
    private String msgName;
    /**
     * 消息通知bid
     */
    private String msgBid;
    /**
     * 备用字段
     */
    private String remake;



    /**
     * 保存
     */
    public String save(CertificateSettingDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setBid(SnowUtil.nextId());
        String dataId = DataInsert.identifier(IDENTIFIER).insert(data);
        return dataId;
    }

    /**
     * 修改
     */
    public void update(CertificateSettingDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        CertificateSettingDo dbData = selectById(data.getBid());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        DataEntity.initFieldValue(IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        DataUpdate.identifier(IDENTIFIER).update(data);
    }

    /**
     * 查看详情
     * @param bid
     * @return
     */
    public CertificateSettingDo selectById(String bid) {
        return DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .oneOrNull(bid, CertificateSettingDo.class);
    }
    /**
     * 删除
     *
     * @param data
     */
    public void delete(CertificateSettingDo data) {
        DataDelete.identifier(IDENTIFIER).softDelete(data.getBid(), System.currentTimeMillis());
    }

    /**
     * 列表查询
     * @param dto
     * @return
     */
    public List<CertificateSettingDo> selectList(CertificateSettingDo dto) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        if (dto.getTypeCode()!=null){
            filter = filter.and(DataFilter.eq("typeCode", dto.getTypeCode()));
        }
        PageResult<CertificateSettingDo> pageResult = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage()
                .queryInvisible().filter(filter,CertificateSettingDo.class);

        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }

    /**
     * 根据字段值查询
     *
     * @param typeCode
     * @param dto
     * @return
     */

    public List<CertificateSettingDo> selectByProperty(String typeCode, List<String> dto) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        if (StringUtils.isNotEmpty(typeCode)){
            filter = filter.and(DataFilter.eq("typeCode", typeCode));
        }
        if (ObjectUtil.isNotEmpty(dto)){
            filter = filter.and(DataFilter.in("msgName", dto));
        }
        PageResult<CertificateSettingDo> pageResult = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage()
                .queryInvisible().filter(filter,CertificateSettingDo.class);

        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }

    /**
     * 保存
     * 因脚本问题，三个设置 全部用一个模型；后续有时间进行修改优化
     * @param data
     */
    public void saveBatch(CeritificateSettingDto data) {
        List<SettingDto> properties = data.getProperties();
        CertificateSettingDo SetDo = new CertificateSettingDo();
        SetDo.setTypeCode(data.getTypeCode());
        List<String> collect = selectList(SetDo).stream().map(CertificateSettingDo::getMsgName).collect(Collectors.toList());

        List<SettingDto> collect1 = properties.stream().filter(t -> !collect.contains(t.getProperty())).collect(Collectors.toList());

        int sort=collect.size();
        for (SettingDto settingDto : collect1) {
            CertificateSettingDo settingDo = new CertificateSettingDo();
            settingDo.setMsgBid(settingDto.getEntity());//模型
            settingDo.setMsgName(settingDto.getProperty());//字段
            settingDo.setCertificateName(settingDto.getName());//字段名称
            settingDo.setTypeCode(data.getTypeCode());//消息类型(emp:人员详细，qua：资格人员，msg：消息)
            settingDo.setRemake(settingDto.getRemake());//是否必填/true 必填
            settingDo.setBid(SnowUtil.nextId());
            settingDo.setCertificateBid(String.valueOf(sort++));
            DataInsert.identifier(IDENTIFIER).insert(settingDo);
        }
        List<String> collect2 = properties.stream().map(SettingDto::getProperty).collect(Collectors.toList());

        List<CertificateSettingDo> collect3 = selectList(SetDo).stream().filter(t -> !collect2.contains(t.getMsgName())).collect(Collectors.toList());
        for (CertificateSettingDo settingDto : collect3) {
            DataDelete.identifier(IDENTIFIER).delete(settingDto.getBid());
        }
    }


    public List<CertificateSettingDo> quertList(List<String> ids) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());

        if (ObjectUtil.isNotEmpty(ids)){
            filter = filter.and(DataFilter.in("bid", ids));
        }

        PageResult<CertificateSettingDo> pageResult = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage()
                .queryInvisible().filter(filter,CertificateSettingDo.class);

        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }
}
