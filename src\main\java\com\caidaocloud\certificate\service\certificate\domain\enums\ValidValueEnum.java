package com.caidaocloud.certificate.service.certificate.domain.enums;

public enum ValidValueEnum {
    Valid( "有效","Valid","ゆうこう"),
    Invalid("无效","Invalid","むこう"),
    InUse("使用中","In Use","しようちゅう"),
    Usable("可使用","Invalid","しようかのう");

    public String zn;
    public String en;
    public String ja;

    ValidValueEnum(String zn, String en, String ja) {
        this.zn=zn;
        this.en=en;
        this.ja=ja;
    }

    public static String  getLanguageTxt(String enumValue,String header) {
       String value= "有效".equals(enumValue)?"Valid":enumValue==null?"":"Invalid";
        if (header.contains("zh-CN")) {
            return ValidValueEnum.valueOf(value).zn;
        } else if (header.contains("en-US")) {
            return ValidValueEnum.valueOf(value).en;
        } else {
            return ValidValueEnum.valueOf(value).ja;
        }
    }

    public static String  getUseLanguageTxt(String enumValue,String header) {
        String value= "可使用".equals(enumValue)?"Usable":enumValue==null?"":"InUse";
        if (header.contains("zh-CN")) {
            return ValidValueEnum.valueOf(value).zn;
        } else if (header.contains("en-US")) {
            return ValidValueEnum.valueOf(value).en;
        } else {
            return ValidValueEnum.valueOf(value).ja;
        }
    }

}
